package com.polarion.synchronizer.proxy.feishu;

import com.lark.project.service.project.builder.ListProjectWorkItemTypeResp;
import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.SynchronizationException;
import com.polarion.synchronizer.model.CreateResult;
import com.polarion.synchronizer.model.FieldDefinition;
import com.polarion.synchronizer.model.IProxy;
import com.polarion.synchronizer.model.Option;
import com.polarion.synchronizer.model.TransferItem;
import com.polarion.synchronizer.model.UpdateResult;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.stream.Collectors;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.time.Instant;



/**
 * 飞书项目代理实现类
 * 负责与飞书项目API的具体交互
 */
public class FeishuProxy implements IProxy {
    
    private static final Logger logger = Logger.getLogger(FeishuProxy.class);
    
    private final FeishuProxyConfiguration configuration;
    private final FeishuSDKHelper sdkHelper;
    
    public FeishuProxy(@NotNull FeishuProxyConfiguration configuration) {
        this.configuration = configuration;

        String configurationError = configuration.checkConfiguration();
        if (configurationError != null) {
            throw new SynchronizationException("飞书项目配置错误: " + configurationError + "。请检查飞书项目连接属性。");
        }

        // 初始化SDK Helper
        FeishuConnection connection = (FeishuConnection) configuration.getConnection();
        if (connection == null) {
            throw new SynchronizationException("飞书项目连接配置不能为空");
        }

        this.sdkHelper = new FeishuSDKHelper(connection);

        logger.info("飞书项目代理初始化完成，项目Key: " + configuration.getProjectKey() +
                   ", 认证模式: " + connection.getAuthMode());
    }

	@Override
	public void close() {
		// 关闭资源，清理连接
		if (sdkHelper != null) {
			logger.debug("关闭飞书项目代理连接");
		}
	}

	@Override
	public List<UpdateResult> delete(List<String> itemIds) {
		List<UpdateResult> results = new ArrayList<>();

		if (itemIds == null || itemIds.isEmpty()) {
			return results;
		}

		logger.debug("开始删除工作项，数量: " + itemIds.size());

		for (String itemId : itemIds) {
			try {
				UpdateResult result = deleteSingleWorkItem(itemId);
				results.add(result);
			} catch (Exception e) {
				logger.error("删除工作项失败: " + itemId, e);
				results.add(new UpdateResult("删除失败: " + e.getMessage()));
			}
		}

		int successCount = (int) results.stream().mapToLong(r -> !r.hasError() ? 1 : 0).sum();
		int failCount = results.size() - successCount;

		logger.info("工作项删除完成，成功: " + successCount + "，失败: " + failCount);

		return results;
	}

	/**
	 * 删除单个工作项
	 */
	private UpdateResult deleteSingleWorkItem(String itemId) {
		try {
			logger.debug("删除工作项: " + itemId);

			// 首先需要获取工作项的类型，因为删除API需要工作项类型
			String workItemType = getWorkItemType(itemId);
			if (workItemType == null) {
				return new UpdateResult("无法获取工作项类型");
			}

			// 构建删除请求
			com.lark.project.service.workitem.builder.DeleteWorkItemReq req =
				com.lark.project.service.workitem.builder.DeleteWorkItemReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(workItemType)
					.workItemID(Long.parseLong(itemId))
					.build();

			// 调用API删除工作项
			com.lark.project.service.workitem.builder.DeleteWorkItemResp resp =
				sdkHelper.getClient().getWorkItemService().deleteWorkItem(req, sdkHelper.getDefaultOptions());

			if (resp.success()) {
				logger.debug("工作项删除成功: " + itemId);
				return new UpdateResult(itemId);
			} else {
				logger.warn("工作项删除失败: " + itemId + ", 错误: " + resp.getErrMsg());
				return new UpdateResult(resp.getErrMsg());
			}

		} catch (NumberFormatException e) {
			logger.error("工作项ID格式错误: " + itemId, e);
			return new UpdateResult("工作项ID格式错误");
		} catch (Exception e) {
			logger.error("删除工作项时发生异常: " + itemId, e);
			return new UpdateResult("删除异常: " + e.getMessage());
		}
	}

	/**
	 * 获取工作项类型
	 */
	private String getWorkItemType(String itemId) {
		try {
			// 使用QueryWorkItemDetail API获取工作项详情来获取类型
			List<Long> workItemIds = new ArrayList<>();
			workItemIds.add(Long.parseLong(itemId));

			com.lark.project.service.workitem.builder.QueryWorkItemDetailReq req =
				com.lark.project.service.workitem.builder.QueryWorkItemDetailReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey("") // 空字符串表示所有类型
					.workItemIDs(workItemIds)
					.build();

			com.lark.project.service.workitem.builder.QueryWorkItemDetailResp resp =
				sdkHelper.getClient().getWorkItemService().queryWorkItemDetail(req, sdkHelper.getDefaultOptions());

			if (resp.success() && resp.getData() != null && !resp.getData().isEmpty()) {
				com.lark.project.service.workitem.model.WorkItemInfo workItem = resp.getData().get(0);
				return workItem.getWorkItemTypeKey();
			}

			logger.warn("无法获取工作项类型: " + itemId);
			return null;

		} catch (Exception e) {
			logger.error("获取工作项类型时发生错误: " + itemId, e);
			return null;
		}
	}

	@Override
	public String getContentScope() {
		// 返回内容范围，通常为null
		return null;
	}

	@Override
	public Collection<FieldDefinition> getDefinedFields(String typeId) {
		try {
			logger.debug("获取工作项类型字段定义: " + typeId);

			// 使用飞书项目API获取字段定义
			com.lark.project.service.field.builder.QueryProjectFieldsReq req =
				com.lark.project.service.field.builder.QueryProjectFieldsReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(typeId)
					.build();

			com.lark.project.service.field.builder.QueryProjectFieldsResp resp =
				sdkHelper.getClient().getFieldService().queryProjectFields(req, sdkHelper.getDefaultOptions());

			if (!resp.success()) {
				logger.warn("获取字段定义失败: " + resp.getErrMsg());
				return getDefaultFieldDefinitions();
			}

			List<FieldDefinition> fieldDefinitions = new ArrayList<>();
			List<com.lark.project.service.field.model.SimpleField> fields = resp.getData();

			if (fields != null) {
				for (com.lark.project.service.field.model.SimpleField field : fields) {
					if (field.getIsObsoleted() != null && field.getIsObsoleted()) {
						continue; // 跳过已废弃的字段
					}

					String fieldKey = field.getFieldKey();
					String fieldName = field.getFieldName() != null ? field.getFieldName() : fieldKey;
					String fieldType = convertFeishuFieldTypeToPolarion(field.getFieldTypeKey());
					boolean isReadOnly = false; // 飞书项目字段一般都可编辑
					boolean isMultiValued = isMultiValuedField(field.getFieldTypeKey());

					FieldDefinition fieldDef = new FieldDefinition(fieldKey, fieldName, fieldType, isReadOnly, isMultiValued);
					fieldDefinitions.add(fieldDef);
				}
			}

			// 添加基本字段
			addBasicFieldDefinitions(fieldDefinitions);

			logger.info("获取到 " + fieldDefinitions.size() + " 个字段定义，工作项类型: " + typeId);
			return fieldDefinitions;

		} catch (Exception e) {
			logger.error("获取字段定义时发生错误，工作项类型: " + typeId, e);
			return getDefaultFieldDefinitions();
		}
	}

	@Override
	public Collection<Option> getDefinedTypes() {
		try {
			logger.debug("获取工作项类型列表");

			// 使用飞书项目API获取工作项类型
			com.lark.project.service.project.builder.ListProjectWorkItemTypeResp resp =
				sdkHelper.getWorkItemTypes(configuration.getProjectKey());

			if (!resp.success()) {
				logger.warn("获取工作项类型失败: " + resp.getErrMsg());
				return getDefaultWorkItemTypes();
			}

			List<Option> options = new ArrayList<>();
			List<com.lark.project.service.workitem.model.WorkItemKeyType> types = resp.getData();

			if (types != null) {
				for (com.lark.project.service.workitem.model.WorkItemKeyType type : types) {
					if (type.getIsDisable() != null && type.getIsDisable() == 1) {
						continue; // 跳过已禁用的类型
					}

					String typeKey = type.getTypeKey();
					String typeName = type.getName() != null ? type.getName() : typeKey;

					Option option = new Option(typeKey, typeName);
					options.add(option);
				}
			}

			logger.info("获取到 " + options.size() + " 个工作项类型");
			return options;

		} catch (Exception e) {
			logger.error("获取工作项类型时发生错误", e);
			return getDefaultWorkItemTypes();
		}
	}

	@Override
	public Collection<Option> getDefinedTypes(Collection<String> requiredTypes) {
		Collection<Option> allTypes = getDefinedTypes();

		if (requiredTypes == null || requiredTypes.isEmpty()) {
			return allTypes;
		}

		// 过滤出需要的类型
		List<Option> filteredTypes = new ArrayList<>();
		Set<String> requiredTypeSet = new HashSet<>(requiredTypes);

		for (Option option : allTypes) {
			if (requiredTypeSet.contains(option.getId())) {
				filteredTypes.add(option);
			}
		}

		logger.debug("过滤后的工作项类型数量: " + filteredTypes.size());
		return filteredTypes;
	}

	@Override
	public Collection<TransferItem> getItems(Collection<String> itemIds, Collection<String> fieldKeys) {
		try {
			logger.debug("获取指定工作项，数量: " + (itemIds != null ? itemIds.size() : 0));

			if (itemIds == null || itemIds.isEmpty()) {
				return new ArrayList<>();
			}

			List<TransferItem> items = new ArrayList<>();

			// 将字符串ID转换为Long类型
			List<Long> workItemIds = new ArrayList<>();
			for (String itemId : itemIds) {
				try {
					workItemIds.add(Long.parseLong(itemId));
				} catch (NumberFormatException e) {
					logger.warn("无效的工作项ID: " + itemId);
				}
			}

			if (workItemIds.isEmpty()) {
				return items;
			}

			// 使用QueryWorkItemDetail API获取工作项详情
			com.lark.project.service.workitem.builder.QueryWorkItemDetailReq req =
				com.lark.project.service.workitem.builder.QueryWorkItemDetailReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey("") // 空字符串表示所有类型
					.workItemIDs(workItemIds)
					.fields(fieldKeys != null ? new ArrayList<>(fieldKeys) : null)
					.build();

			com.lark.project.service.workitem.builder.QueryWorkItemDetailResp resp =
				sdkHelper.getClient().getWorkItemService().queryWorkItemDetail(req, sdkHelper.getDefaultOptions());

			if (!resp.success()) {
				logger.warn("获取工作项详情失败: " + resp.getErrMsg());
				return items;
			}

			// 转换工作项数据
			List<com.lark.project.service.workitem.model.WorkItemInfo> workItems = resp.getData();
			if (workItems != null) {
				for (com.lark.project.service.workitem.model.WorkItemInfo workItem : workItems) {
					TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
					if (item != null) {
						items.add(item);
					}
				}
			}

			logger.info("成功获取 " + items.size() + " 个工作项");
			return items;

		} catch (Exception e) {
			logger.error("获取指定工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public Collection<TransferItem> getScopeItems(Collection<String> fieldKeys) {
		try {
			logger.debug("获取范围内所有工作项");

			List<TransferItem> allItems = new ArrayList<>();

			// 使用Filter API获取工作项列表
			com.lark.project.service.workitem.builder.FilterReq req =
				com.lark.project.service.workitem.builder.FilterReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.pageNum(1L)
					.pageSize(100L) // 每页100个工作项
					.build();

			// 如果配置了工作项类型过滤，添加类型过滤
			if (configuration.getWorkItemTypes() != null && !configuration.getWorkItemTypes().isEmpty()) {
				String[] typeArray = configuration.getWorkItemTypes().split(",");
				List<String> typeList = new ArrayList<>();
				for (String type : typeArray) {
					typeList.add(type.trim());
				}
				req.getFilterReqBody().setWorkItemTypeKeys(typeList);
			}

			// 分页获取所有工作项
			int currentPage = 1;
			boolean hasMore = true;

			while (hasMore) {
				req.getFilterReqBody().setPageNum((long) currentPage);

				com.lark.project.service.workitem.builder.FilterResp resp =
					sdkHelper.getClient().getWorkItemService().filter(req, sdkHelper.getDefaultOptions());

				if (!resp.success()) {
					logger.warn("获取工作项列表失败: " + resp.getErrMsg());
					break;
				}

				List<com.lark.project.service.workitem.model.WorkItemInfo> workItems = resp.getData();
				if (workItems != null && !workItems.isEmpty()) {
					for (com.lark.project.service.workitem.model.WorkItemInfo workItem : workItems) {
						TransferItem item = convertWorkItemToTransferItem(workItem, fieldKeys);
						if (item != null) {
							allItems.add(item);
						}
					}

					// 检查是否还有更多页面
					if (resp.getPagination() != null) {
						// 检查分页信息，如果当前页数量等于页面大小，可能还有更多
						hasMore = workItems.size() >= 100;
					} else {
						hasMore = workItems.size() >= 100; // 如果返回的数量等于页面大小，可能还有更多
					}

					currentPage++;
				} else {
					hasMore = false;
				}
			}

			logger.info("成功获取 " + allItems.size() + " 个范围内工作项");
			return allItems;

		} catch (Exception e) {
			logger.error("获取范围内工作项时发生错误", e);
			return new ArrayList<>();
		}
	}

	@Override
	public String getTargetName() {
		// 返回目标系统名称
		return "飞书项目 - " + configuration.getProjectKey();
	}

	@Override
	public boolean hasNonSynchronizableFields() {
		// 飞书项目的字段一般都可以同步
		return false;
	}

	@Override
	public boolean isHierarchySupported() {
		// 飞书项目不支持层次结构
		return false;
	}

	@Override
	public List<UpdateResult> update(List<TransferItem> items) {
		List<UpdateResult> results = new ArrayList<>();

		if (items == null || items.isEmpty()) {
			return results;
		}

		logger.debug("开始处理工作项，数量: " + items.size());

		for (TransferItem item : items) {
			try {
				UpdateResult result;

				// 判断是创建还是更新
				if (item.getId() == null || item.getKey().isForeign) {
					// 创建新工作项
					result = createSingleWorkItem(item);
				} else {
					// 更新现有工作项
					result = updateSingleWorkItem(item);
				}

				results.add(result);
			} catch (Exception e) {
				logger.error("处理工作项失败: " + item.getId(), e);
				results.add(new UpdateResult("处理失败: " + e.getMessage()));
			}
		}

		int successCount = (int) results.stream().mapToLong(r -> !r.hasError() ? 1 : 0).sum();
		int failCount = results.size() - successCount;

		logger.info("工作项处理完成，成功: " + successCount + "，失败: " + failCount);

		return results;
	}

	/**
	 * 创建单个工作项
	 */
	private UpdateResult createSingleWorkItem(TransferItem item) {
		try {
			logger.debug("创建新工作项，类型: " + item.getType());

			// 验证必需字段
			String workItemType = item.getType();
			if (workItemType == null || workItemType.trim().isEmpty()) {
				workItemType = "task"; // 默认类型为task
				logger.debug("工作项类型为空，使用默认类型: " + workItemType);
			}

			String workItemTitle = (String) item.getValue("title");
			if (workItemTitle == null || workItemTitle.trim().isEmpty()) {
				workItemTitle = (String) item.getValue("name");
				if (workItemTitle == null || workItemTitle.trim().isEmpty()) {
					workItemTitle = "新工作项"; // 默认标题
					logger.debug("工作项标题为空，使用默认标题: " + workItemTitle);
				}
			}

			// 验证工作项类型是否有效
			if (!isValidWorkItemType(workItemType)) {
				logger.warn("无效的工作项类型: " + workItemType + "，使用默认类型: task");
				workItemType = "task";
			}

			// 构建创建请求
			com.lark.project.service.workitem.builder.CreateWorkItemReq req =
				com.lark.project.service.workitem.builder.CreateWorkItemReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(workItemType)
					.name(workItemTitle)
					.build();

			// 设置字段值
			List<com.lark.project.service.field.model.FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 设置描述
			String description = (String) item.getValue("description");
			if (description != null) {
				com.lark.project.service.field.model.FieldValuePair descField = new com.lark.project.service.field.model.FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(description);
				fieldValuePairs.add(descField);
			}

			// 设置自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						String fieldKey = entry.getKey();
						Object fieldValue = entry.getValue();

						// 跳过只读字段和系统字段
						if (IProxy.KEY_ITEM_URL.equals(fieldKey) ||
							"work_item_id".equals(fieldKey) ||
							"created_at".equals(fieldKey) ||
							"updated_at".equals(fieldKey) ||
							"creator".equals(fieldKey)) {
							continue;
						}

						// 验证字段值
						if (!isValidFieldValue(fieldKey, fieldValue)) {
							logger.warn("跳过无效字段值: " + fieldKey + " = " + fieldValue);
							continue;
						}

						Object convertedValue = convertPolarionValueToFeishu(fieldValue);
						if (convertedValue != null) {
							com.lark.project.service.field.model.FieldValuePair customField = new com.lark.project.service.field.model.FieldValuePair();
							customField.setFieldKey(fieldKey);
							customField.setFieldValue(convertedValue);
							fieldValuePairs.add(customField);
						}
					}
				}
			}

			req.getCreateWorkItemReqBody().setFieldValuePairs(fieldValuePairs);

			// 调用API创建工作项
			com.lark.project.service.workitem.builder.CreateWorkItemResp resp =
				sdkHelper.getClient().getWorkItemService().createWorkItem(req, sdkHelper.getDefaultOptions());

			if (resp.success()) {
				Long createdId = resp.getData();
				String createdIdStr = createdId != null ? createdId.toString() : null;

				// 更新TransferItem的ID
				item.setId(createdIdStr);

				logger.debug("工作项创建成功: " + createdIdStr);
				return new CreateResult(createdIdStr, null);
			} else {
				logger.warn("工作项创建失败，错误: " + resp.getErrMsg());
				return new CreateResult(null, resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("创建工作项时发生异常", e);
			return new CreateResult(null, "创建异常: " + e.getMessage());
		}
	}

	/**
	 * 更新单个工作项
	 */
	private UpdateResult updateSingleWorkItem(TransferItem item) {
		try {
			// 构建更新请求
			com.lark.project.service.workitem.builder.UpdateWorkItemReq req =
				com.lark.project.service.workitem.builder.UpdateWorkItemReq.newBuilder()
					.projectKey(configuration.getProjectKey())
					.workItemTypeKey(item.getType() != null ? item.getType() : "")
					.workItemID(Long.parseLong(item.getId()))
					.build();

			// 设置更新字段
			List<com.lark.project.service.field.model.FieldValuePair> fieldValuePairs = new ArrayList<>();

			// 更新标题
			String title = (String) item.getValue("title");
			if (title == null) {
				title = (String) item.getValue("name");
			}
			if (title != null) {
				com.lark.project.service.field.model.FieldValuePair titleField = new com.lark.project.service.field.model.FieldValuePair();
				titleField.setFieldKey("name");
				titleField.setFieldValue(title);
				fieldValuePairs.add(titleField);
			}

			// 更新描述
			String description = (String) item.getValue("description");
			if (description != null) {
				com.lark.project.service.field.model.FieldValuePair descField = new com.lark.project.service.field.model.FieldValuePair();
				descField.setFieldKey("description");
				descField.setFieldValue(description);
				fieldValuePairs.add(descField);
			}

			// 更新自定义字段
			if (item instanceof Item) {
				Item itemImpl = (Item) item;
				Map<String, Object> customFields = itemImpl.getCustomFields();
				if (customFields != null) {
					for (Map.Entry<String, Object> entry : customFields.entrySet()) {
						String fieldKey = entry.getKey();
						Object fieldValue = entry.getValue();

						// 跳过只读字段和系统字段
						if (IProxy.KEY_ITEM_URL.equals(fieldKey) ||
							"work_item_id".equals(fieldKey) ||
							"created_at".equals(fieldKey) ||
							"updated_at".equals(fieldKey) ||
							"creator".equals(fieldKey)) {
							continue;
						}

						// 验证字段值
						if (!isValidFieldValue(fieldKey, fieldValue)) {
							logger.warn("跳过无效字段值: " + fieldKey + " = " + fieldValue);
							continue;
						}

						Object convertedValue = convertPolarionValueToFeishu(fieldValue);
						if (convertedValue != null) {
							com.lark.project.service.field.model.FieldValuePair customField = new com.lark.project.service.field.model.FieldValuePair();
							customField.setFieldKey(fieldKey);
							customField.setFieldValue(convertedValue);
							fieldValuePairs.add(customField);
						}
					}
				}
			}

			req.getUpdateWorkItemReqBody().setFieldValuePairs(fieldValuePairs);

			// 调用API更新工作项
			com.lark.project.service.workitem.builder.UpdateWorkItemResp resp =
				sdkHelper.getClient().getWorkItemService().updateWorkItem(req, sdkHelper.getDefaultOptions());

			if (resp.success()) {
				logger.debug("工作项更新成功: " + item.getId());
				return UpdateResult.success();
			} else {
				logger.warn("工作项更新失败: " + item.getId() + ", 错误: " + resp.getErrMsg());
				return new UpdateResult(item.getId(), resp.getErrMsg());
			}

		} catch (Exception e) {
			logger.error("更新工作项时发生异常: " + item.getId(), e);
			return new UpdateResult(item.getId(), "更新异常: " + e.getMessage());
		}
	}

	/**
	 * 将飞书项目字段类型转换为Polarion字段类型
	 */
	private String convertFeishuFieldTypeToPolarion(String feishuFieldType) {
		if (feishuFieldType == null) {
			return IProxy.TYPE_STRING;
		}

		switch (feishuFieldType.toLowerCase()) {
			case "text":
			case "string":
			case "textarea":
				return IProxy.TYPE_STRING;
			case "rich_text":
			case "richtext":
				return IProxy.TYPE_RICH_TEXT;
			case "number":
			case "integer":
				return IProxy.TYPE_INTEGER;
			case "float":
			case "decimal":
				return IProxy.TYPE_FLOAT;
			case "date":
				return IProxy.TYPE_DATE;
			case "datetime":
			case "timestamp":
				return IProxy.TYPE_DATE_TIME;
			case "boolean":
				return IProxy.TYPE_BOOLEAN;
			case "user":
			case "person":
				return IProxy.TYPE_USER;
			case "option":
			case "enum":
			case "select":
				return IProxy.TYPE_OPTION;
			case "multi_option":
			case "multi_select":
				return IProxy.TYPE_OPTION; // 多选也使用option类型，通过isMultiValued区分
			default:
				logger.debug("未知的飞书字段类型: " + feishuFieldType + "，使用默认类型string");
				return IProxy.TYPE_STRING;
		}
	}

	/**
	 * 判断字段是否为多值字段
	 */
	private boolean isMultiValuedField(String feishuFieldType) {
		if (feishuFieldType == null) {
			return false;
		}

		return feishuFieldType.toLowerCase().contains("multi") ||
			   feishuFieldType.toLowerCase().contains("array") ||
			   feishuFieldType.toLowerCase().contains("list");
	}

	/**
	 * 添加基本字段定义
	 */
	private void addBasicFieldDefinitions(List<FieldDefinition> fieldDefinitions) {
		// 添加基本的必需字段
		fieldDefinitions.add(new FieldDefinition("work_item_id", "工作项ID", IProxy.TYPE_STRING, true, false));
		fieldDefinitions.add(new FieldDefinition("name", "标题", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("description", "描述", IProxy.TYPE_RICH_TEXT, false, false));
		fieldDefinitions.add(new FieldDefinition("work_item_type_key", "类型", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("status", "状态", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("priority", "优先级", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("assignee", "指派人", IProxy.TYPE_USER, false, false));
		fieldDefinitions.add(new FieldDefinition("reporter", "报告人", IProxy.TYPE_USER, false, false));
		fieldDefinitions.add(new FieldDefinition("creator", "创建人", IProxy.TYPE_USER, true, false));
		fieldDefinitions.add(new FieldDefinition("created_at", "创建时间", IProxy.TYPE_DATE_TIME, true, false));
		fieldDefinitions.add(new FieldDefinition("updated_at", "更新时间", IProxy.TYPE_DATE_TIME, true, false));
		fieldDefinitions.add(new FieldDefinition("due_date", "截止日期", IProxy.TYPE_DATE, false, false));

		// 添加URL字段用于链接
		fieldDefinitions.add(new FieldDefinition(IProxy.KEY_ITEM_URL, "项目链接", IProxy.TYPE_STRING, true, false));
	}

	/**
	 * 获取默认字段定义（当API调用失败时使用）
	 */
	private Collection<FieldDefinition> getDefaultFieldDefinitions() {
		List<FieldDefinition> fieldDefinitions = new ArrayList<>();
		addBasicFieldDefinitions(fieldDefinitions);

		// 添加一些常用的自定义字段
		fieldDefinitions.add(new FieldDefinition("story_points", "故事点", IProxy.TYPE_INTEGER, false, false));
		fieldDefinitions.add(new FieldDefinition("labels", "标签", IProxy.TYPE_OPTION, false, true));
		fieldDefinitions.add(new FieldDefinition("component", "组件", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("version", "版本", IProxy.TYPE_OPTION, false, false));
		fieldDefinitions.add(new FieldDefinition("epic", "史诗", IProxy.TYPE_STRING, false, false));
		fieldDefinitions.add(new FieldDefinition("sprint", "迭代", IProxy.TYPE_STRING, false, false));

		logger.info("使用默认字段定义，共 " + fieldDefinitions.size() + " 个字段");
		return fieldDefinitions;
	}

	/**
	 * 获取默认工作项类型（当API调用失败时使用）
	 */
	private Collection<Option> getDefaultWorkItemTypes() {
		List<Option> options = new ArrayList<>();

		// 添加常见的工作项类型
		options.add(new Option("story", "用户故事"));
		options.add(new Option("task", "任务"));
		options.add(new Option("bug", "缺陷"));
		options.add(new Option("epic", "史诗"));
		options.add(new Option("feature", "特性"));
		options.add(new Option("requirement", "需求"));
		options.add(new Option("test_case", "测试用例"));
		options.add(new Option("issue", "问题"));

		logger.info("使用默认工作项类型，共 " + options.size() + " 个类型");
		return options;
	}

	/**
	 * 将Polarion字段值转换为飞书项目字段值
	 */
	private Object convertPolarionValueToFeishu(Object polarionValue) {
		if (polarionValue == null) {
			return null;
		}

		try {
			// 处理日期类型
			if (polarionValue instanceof Date) {
				Date date = (Date) polarionValue;
				return date.getTime() / 1000; // 飞书使用秒级时间戳
			}

			// 处理布尔类型
			if (polarionValue instanceof Boolean) {
				return polarionValue;
			}

			// 处理数字类型
			if (polarionValue instanceof Number) {
				return polarionValue;
			}

			// 处理用户类型
			if (polarionValue instanceof String) {
				String strValue = (String) polarionValue;

				// 检查是否是用户ID格式
				if (strValue.startsWith("user:") || strValue.contains("@")) {
					return convertUserValue(strValue);
				}

				// 检查是否是选项值格式
				if (strValue.contains("|") || strValue.startsWith("option:")) {
					return convertOptionValue(strValue);
				}

				// 普通字符串值
				return strValue;
			}

			// 处理集合类型
			if (polarionValue instanceof Collection) {
				Collection<?> collection = (Collection<?>) polarionValue;
				List<Object> convertedList = new ArrayList<>();

				for (Object item : collection) {
					Object convertedItem = convertPolarionValueToFeishu(item);
					if (convertedItem != null) {
						convertedList.add(convertedItem);
					}
				}

				return convertedList;
			}

			// 其他类型转换为字符串
			return polarionValue.toString();

		} catch (Exception e) {
			logger.warn("转换Polarion字段值时发生错误: " + polarionValue, e);
			return polarionValue.toString();
		}
	}

	/**
	 * 转换用户值
	 */
	private Object convertUserValue(String userValue) {
		try {
			// 移除user:前缀
			String cleanUserValue = userValue.startsWith("user:") ? userValue.substring(5) : userValue;

			// 如果是邮箱格式，尝试查找对应的用户ID
			if (cleanUserValue.contains("@")) {
				// TODO: 可以通过飞书用户API查找用户ID
				logger.debug("用户邮箱格式: " + cleanUserValue);
				return cleanUserValue;
			}

			// 直接返回用户标识
			return cleanUserValue;

		} catch (Exception e) {
			logger.warn("转换用户值时发生错误: " + userValue, e);
			return userValue;
		}
	}

	/**
	 * 转换选项值
	 */
	private Object convertOptionValue(String optionValue) {
		try {
			// 移除option:前缀
			String cleanOptionValue = optionValue.startsWith("option:") ? optionValue.substring(7) : optionValue;

			// 处理多选项（用|分隔）
			if (cleanOptionValue.contains("|")) {
				String[] options = cleanOptionValue.split("\\|");
				List<String> optionList = new ArrayList<>();
				for (String option : options) {
					optionList.add(option.trim());
				}
				return optionList;
			}

			// 单选项
			return cleanOptionValue;

		} catch (Exception e) {
			logger.warn("转换选项值时发生错误: " + optionValue, e);
			return optionValue;
		}
	}

	/**
	 * 将飞书工作项转换为TransferItem
	 */
	private TransferItem convertWorkItemToTransferItem(com.lark.project.service.workitem.model.WorkItemInfo workItem, Collection<String> fieldKeys) {
		try {
			String itemId = workItem.getID() != null ? workItem.getID().toString() : null;
			if (itemId == null) {
				logger.warn("工作项ID为空，跳过转换");
				return null;
			}

			Item item = new Item(itemId);

			// 设置基本属性
			if (workItem.getName() != null) {
				item.put("title", workItem.getName());
				item.put("name", workItem.getName());
			}

			if (workItem.getWorkItemTypeKey() != null) {
				item.put("type", workItem.getWorkItemTypeKey());
			}

			// 设置时间戳
			if (workItem.getCreatedAt() != null) {
				item.put("created_at", new Date(workItem.getCreatedAt() * 1000)); // 飞书时间戳是秒，Java需要毫秒
			}

			if (workItem.getUpdatedAt() != null) {
				item.put("updated_at", new Date(workItem.getUpdatedAt() * 1000));
			}

			// 设置创建者和更新者
			if (workItem.getCreatedBy() != null) {
				item.put("reporter", workItem.getCreatedBy());
				item.put("creator", workItem.getCreatedBy());
			}

			if (workItem.getUpdatedBy() != null) {
				item.put("updated_by", workItem.getUpdatedBy());
			}

			// 设置状态信息
			if (workItem.getWorkItemStatus() != null) {
				// 飞书项目的状态对象可能没有getStatus方法，直接使用状态对象
				item.put("status", workItem.getWorkItemStatus());
			}

			if (workItem.getSubStage() != null) {
				item.put("sub_stage", workItem.getSubStage());
			}

			// 处理自定义字段
			if (workItem.getFields() != null) {
				for (com.lark.project.service.field.model.FieldValuePair field : workItem.getFields()) {
					if (field.getFieldKey() != null && field.getFieldValue() != null) {
						// 根据字段类型处理字段值
						Object fieldValue = convertFieldValue(field);
						if (fieldValue != null) {
							item.put(field.getFieldKey(), fieldValue);
						}
					}
				}
			}

			// 设置项目链接
			String itemUrl = buildWorkItemUrl(workItem);
			if (itemUrl != null) {
				item.put(IProxy.KEY_ITEM_URL, itemUrl);
			}

			// 过滤字段（如果指定了fieldKeys）
			if (fieldKeys != null && !fieldKeys.isEmpty()) {
				filterTransferItemFields(item, fieldKeys);
			}

			return item;

		} catch (Exception e) {
			logger.error("转换工作项时发生错误: " + workItem.getID(), e);
			return null;
		}
	}

	/**
	 * 转换字段值
	 */
	private Object convertFieldValue(com.lark.project.service.field.model.FieldValuePair field) {
		try {
			Object value = field.getFieldValue();
			if (value == null) {
				return null;
			}

			// 根据字段类型进行转换
			String fieldKey = field.getFieldKey();

			// 处理特殊字段
			if ("description".equals(fieldKey) && value instanceof String) {
				return value; // 描述字段保持原样
			}

			// 处理日期字段
			if (fieldKey.contains("date") || fieldKey.contains("time")) {
				if (value instanceof Number) {
					long timestamp = ((Number) value).longValue();
					// 飞书时间戳可能是秒或毫秒，自动判断
					if (timestamp < 10000000000L) { // 小于10位数，认为是秒级时间戳
						timestamp *= 1000;
					}
					return new Date(timestamp);
				} else if (value instanceof String) {
					// 尝试解析字符串格式的日期
					return parseDateString((String) value);
				}
			}

			// 处理数字字段
			if (value instanceof Number) {
				return value;
			}

			// 处理布尔字段
			if (value instanceof Boolean) {
				return value;
			}

			// 其他情况转换为字符串
			return value.toString();

		} catch (Exception e) {
			logger.warn("转换字段值时发生错误: " + field.getFieldKey(), e);
			return null;
		}
	}

	/**
	 * 构建工作项URL
	 */
	private String buildWorkItemUrl(com.lark.project.service.workitem.model.WorkItemInfo workItem) {
		try {
			FeishuConnection connection = (FeishuConnection) configuration.getConnection();
			String baseUrl = connection.getServerUrl();
			if (baseUrl == null) {
				baseUrl = "https://project.feishu.cn";
			}

			// 构建飞书项目工作项URL
			return baseUrl + "/project/" + workItem.getProjectKey() + "/workitem/" + workItem.getID();

		} catch (Exception e) {
			logger.warn("构建工作项URL时发生错误", e);
			return null;
		}
	}

	/**
	 * 过滤TransferItem字段
	 */
	private void filterTransferItemFields(TransferItem item, Collection<String> fieldKeys) {
		if (fieldKeys == null || fieldKeys.isEmpty()) {
			return; // 没有指定字段过滤，保留所有字段
		}

		try {
			Set<String> allowedFields = new HashSet<>(fieldKeys);
			Map<String, Object> values = item.getValues();

			if (values != null) {
				// 创建新的字段映射，只包含允许的字段
				Map<String, Object> filteredFields = new HashMap<>();

				for (Map.Entry<String, Object> entry : values.entrySet()) {
					String fieldKey = entry.getKey();

					// 保留在允许列表中的字段
					if (allowedFields.contains(fieldKey)) {
						filteredFields.put(fieldKey, entry.getValue());
					}
					// 保留基本系统字段
					else if (isSystemField(fieldKey)) {
						filteredFields.put(fieldKey, entry.getValue());
					}
				}

				// 清空原有字段并设置过滤后的字段
				values.clear();
				values.putAll(filteredFields);
			}

			// 过滤基本属性字段
			if (!allowedFields.contains("title") && !allowedFields.contains("name")) {
				item.getValues().remove("title");
				item.getValues().remove("name");
			}

			if (!allowedFields.contains("description")) {
				item.getValues().remove("description");
			}

			if (!allowedFields.contains("type") && !allowedFields.contains("work_item_type_key")) {
				item.getValues().remove("type");
			}

			logger.debug("字段过滤完成，保留字段数量: " + (values != null ? values.size() : 0));

		} catch (Exception e) {
			logger.warn("字段过滤时发生错误", e);
		}
	}

	/**
	 * 判断是否为系统字段
	 */
	private boolean isSystemField(String fieldKey) {
		if (fieldKey == null) {
			return false;
		}

		// 系统字段列表
		Set<String> systemFields = Set.of(
			"work_item_id",
			"created_at",
			"updated_at",
			"creator",
			"updated_by",
			IProxy.KEY_ITEM_URL,
			"id",
			"key"
		);

		return systemFields.contains(fieldKey);
	}

	/**
	 * 验证工作项类型是否有效
	 */
	private boolean isValidWorkItemType(String workItemType) {
		if (workItemType == null || workItemType.trim().isEmpty()) {
			return false;
		}

		try {
			// 获取所有可用的工作项类型
			Collection<Option> availableTypes = getDefinedTypes();

			if (availableTypes != null) {
				for (Option option : availableTypes) {
					if (workItemType.equals(option.getId())) {
						return true;
					}
				}
			}

			// 如果无法获取类型列表，允许常见的类型
			Set<String> commonTypes = Set.of(
				"task", "story", "bug", "epic", "feature",
				"requirement", "test_case", "issue", "subtask"
			);

			return commonTypes.contains(workItemType.toLowerCase());

		} catch (Exception e) {
			logger.warn("验证工作项类型时发生错误: " + workItemType, e);
			// 发生错误时，允许常见类型
			return Set.of("task", "story", "bug", "epic", "feature").contains(workItemType.toLowerCase());
		}
	}

	/**
	 * 验证字段值是否有效
	 */
	private boolean isValidFieldValue(String fieldKey, Object fieldValue) {
		if (fieldKey == null || fieldValue == null) {
			return false;
		}

		try {
			// 检查字段值长度限制
			if (fieldValue instanceof String) {
				String strValue = (String) fieldValue;

				// 标题字段长度限制
				if ("name".equals(fieldKey) || "title".equals(fieldKey)) {
					return strValue.length() <= 255;
				}

				// 描述字段长度限制
				if ("description".equals(fieldKey)) {
					return strValue.length() <= 65535;
				}

				// 一般字符串字段长度限制
				return strValue.length() <= 1000;
			}

			// 数字字段范围检查
			if (fieldValue instanceof Number) {
				Number numValue = (Number) fieldValue;

				// 检查是否在合理范围内
				if (fieldValue instanceof Integer || fieldValue instanceof Long) {
					long longValue = numValue.longValue();
					return longValue >= Integer.MIN_VALUE && longValue <= Integer.MAX_VALUE;
				}

				if (fieldValue instanceof Float || fieldValue instanceof Double) {
					double doubleValue = numValue.doubleValue();
					return !Double.isInfinite(doubleValue) && !Double.isNaN(doubleValue);
				}
			}

			// 日期字段检查
			if (fieldValue instanceof Date) {
				Date dateValue = (Date) fieldValue;
				// 检查日期是否在合理范围内（1970-2100年）
				long timestamp = dateValue.getTime();
				return timestamp > 0 && timestamp < 4102444800000L; // 2100年1月1日
			}

			return true;

		} catch (Exception e) {
			logger.warn("验证字段值时发生错误: " + fieldKey + " = " + fieldValue, e);
			return false;
		}
	}

	/**
	 * 解析日期字符串
	 */
	private Date parseDateString(String dateStr) {
		if (dateStr == null || dateStr.trim().isEmpty()) {
			return null;
		}

		try {
			// 尝试解析ISO 8601格式
			if (dateStr.contains("T")) {
				return java.time.Instant.parse(dateStr).toEpochMilli() > 0 ?
					Date.from(java.time.Instant.parse(dateStr)) : null;
			}

			// 尝试解析时间戳字符串
			try {
				long timestamp = Long.parseLong(dateStr);
				if (timestamp < 10000000000L) { // 秒级时间戳
					timestamp *= 1000;
				}
				return new Date(timestamp);
			} catch (NumberFormatException e) {
				// 不是时间戳格式，继续尝试其他格式
			}

			// 尝试解析常见日期格式
			String[] patterns = {
				"yyyy-MM-dd HH:mm:ss",
				"yyyy-MM-dd'T'HH:mm:ss",
				"yyyy-MM-dd'T'HH:mm:ss.SSS",
				"yyyy-MM-dd",
				"MM/dd/yyyy",
				"dd/MM/yyyy"
			};

			for (String pattern : patterns) {
				try {
					java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(pattern);
					return sdf.parse(dateStr);
				} catch (java.text.ParseException e) {
					// 继续尝试下一个格式
				}
			}

			logger.warn("无法解析日期字符串: " + dateStr);
			return null;

		} catch (Exception e) {
			logger.warn("解析日期字符串时发生错误: " + dateStr, e);
			return null;
		}
	}
    
  
}
